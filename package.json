{"private": true, "author": "shun.cao <<EMAIL>>", "scripts": {"dev": "max dev", "build": "max build", "format": "prettier --cache --write .", "prepare": "husky", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@umijs/max": "^4.3.35", "antd": "^5.4.0", "ethers": "^5.7.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/lodash": "^4.17.13", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}