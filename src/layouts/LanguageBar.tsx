import { LANGUAGE_LIST } from '@/constants/publicConstant';
import { TranslationOutlined } from '@ant-design/icons';
import { getLocale, setLocale } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Button, Dropdown } from 'antd';
import * as dayjs from 'dayjs';
import 'dayjs/locale/en';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/zh-tw';
import { useEffect, useState } from 'react';

interface IItem {
  key: string;
  label: string;
  disabled: boolean;
}

const LanguageBar = () => {
  const [items, setItems] = useState<IItem[]>(LANGUAGE_LIST);

  useEffect(() => {
    handleLocales(getLocale());
  }, []);

  const handleLocales = (locale: string) => {
    setItems(
      items.map((item) => ({
        ...item,
        disabled: item.key === locale,
      })),
    );
  };

  const onClick: MenuProps['onClick'] = ({ key: locale }) => {
    handleLocales(locale);
    setLocale(locale, false);
    if (locale) {
      dayjs.locale(locale);
    } else {
      dayjs.locale(LANGUAGE_LIST.CN.label);
    }
  };

  return (
    <Dropdown menu={{ items, onClick }}>
      <Button
        size="large"
        color="default"
        variant="link"
        style={{ color: '#fff' }}
        icon={<TranslationOutlined />}
      />
    </Dropdown>
  );
};

export default LanguageBar;
