/**
 * 接口常量
 */
export default {
  // 机构号
  BASE_ORG_PARAM: {
    LIST: {
      URL: 'param/baseOrg/query',
    },
  },
  // 接口配置参数应用服务
  INTERFACE_CONFIG: {
    LIST: 'nq/gateway/interfaceConfigParamFind',
    CREATE: 'nm/gateway/interfaceConfigParamAdd',
    EDIT: 'nm/gateway/interfaceConfigParamUpdate',
    DELETE: 'nm/gateway/interfaceConfigParamDelete',
  },
  // 接口映射参数应用服务
  INTERFACE_MAPPING: {
    LIST: 'nq/gateway/interfaceMapParamFind',
    CREATE: 'nm/gateway/interfaceMapParamAdd',
    EDIT: 'nm/gateway/interfaceMapParamUpdate',
    DELETE: 'nm/gateway/interfaceMapParamDelete',
  },
  // 双发白名单服务
  DOUBLE_WHITE_LIST: {
    LIST: 'nq/gateway/dupluWhiteParamFind',
    CREATE: 'nm/gateway/dupluWhiteParamAdd',
    EDIT: 'nm/gateway/dupluWhiteParamUpdate',
    DELETE: 'nm/gateway/dupluWhiteParamDelete',
  },
  // 错误代码映射参数
  ERROR_CODE_MAP: {
    LIST: 'nq/gateway/errorCodeMapParamFind',
    CREATE: 'nm/gateway/errorCodeMapParamAdd',
    EDIT: 'nm/gateway/errorCodeMapParamUpdate',
    DELETE: 'nm/gateway/errorCodeMapParamDelete',
  },
  // 转换配置查找
  CONVER_CONFIG: {
    LIST: 'nq/gateway/convertConfigFind',
    CREATE: 'nm/gateway/convertConfigAdd',
    EDIT: 'nm/gateway/convertConfigUpdate',
    DELETE: 'nm/gateway/convertConfigDelete',
  },
  // 查找配置参数
  SEARCH_CONFIG: {
    LIST: 'nq/gateway/configParamFind',
    CREATE: 'nm/gateway/configParamAdd',
    EDIT: 'nm/gateway/configParamUpdate',
    DELETE: 'nm/gateway/configParamDelete',
  },
  // api密钥映射应用服务
  API_KEY_MAPPING: {
    LIST: 'nq/gateway/apiKeyMapFind',
    CREATE: 'nm/gateway/apiKeyMapAdd',
    EDIT: 'nm/gateway/apiKeyMapUpdate',
    DELETE: 'nm/gateway/apiKeyMapDelete',
  },
  // api密钥配置应用服务
  API_KEY_CONFIG: {
    LIST: 'nq/gateway/apiKeyConfigFind',
    CREATE: 'nm/gateway/apiKeyConfigAdd',
    EDIT: 'nm/gateway/apiKeyConfigUpdate',
    DELETE: 'nm/gateway/apiKeyConfigDelete',
  },
  // 交易网关渠道管理-签入签出
  TRANSACTION_SIGN: {
    EDIT: 'anytxn-trx-gateway/nq/gateway/mgrPlatform',
  },
  // 交易网关渠道管理-签到状态查询
  TRANSACTION_CHANEL_SIGN: {
    LIST: 'anytxn-trx-gateway/nq/gateway/queryChanelState',
  },
  // 出口网关渠道管理-签入签出
  EXPORT_SIGN: {
    EDIT: 'anytxn-export-gateway/nq/gateway/export/mgrPlatform',
  },
  // 出口网关渠道管理-签到状态查询
  EXPORT_CHANEL_SIGN: {
    LIST: 'anytxn-export-gateway/nq/gateway/export/queryChanelState',
  },
  // 角色管理
  ROLE_MANAGE: {
    LIST: '/role/getRoleInfo',
    EDIT: '/role/updateByRoleId',
    CREATE: '/role/addRole',
    EDIT_MOUNT: '/rolePermission/save',
  },
};
