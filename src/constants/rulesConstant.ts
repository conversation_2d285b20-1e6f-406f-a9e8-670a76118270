/**
 * 规则引擎常量
 */

import { OPERATE_TYPE } from './publicConstant';

/**
 * 规则模块枚举
 */
export const RULE_TYPE = {
  limit: 'limit',
  auth: 'auth',
};

/**
 * 计算表达式运算符枚举
 */
export const OPEXP_TYPE = {
  MIN: 'MIN',
  MAX: 'MAX',
  SUM: 'SUM',
};

/**
 * 使用mathjs解析的类型枚举
 */
export const MATH_NODE_TYPE = {
  OperatorNode: 'OperatorNode',
  FunctionNode: 'FunctionNode',
  ConstantNode: 'ConstantNode',
};

/**
 * 规则配置类型枚举
 */
export const RULE_OPTION_TYPE = {
  judgeExps: 'judgeExps',
  opExps: 'opExps',
  ruleResultField: 'ruleResultField',
};

export const RULE_OPTION_LIST = {
  opExps: 'OPERATOR',
  judgeExps: 'JUDGE',
  ruleResultField: 'RESULT',
};

/**
 * 判断表达式节点枚举
 */
export const RULE_COND_NODE_TYPE = {
  Condition: 'Condition',
  ConditionGroup: 'ConditionGroup',
};

export const RULE_COND_NODE_LIST = {
  Condition: 1,
  ConditionGroup: 2,
};

/**
 * 条件/条件组枚举
 */
export const RULE_GROUP_TYPE = {
  ALL: 'ALL',
  ANY: 'ANY',
};

/**
 * 条件选单枚举
 */
export const COND_MENU_TYPE = {
  addCond: 'addCond',
  addCondGroup: 'addCondGroup',
  delete: OPERATE_TYPE.delete,
};

/**
 * 规则因子类型枚举
 */
export const RULE_FAC_LIST = {
  INTEGER: 'INT_X',
  LONGINTEGER: 'LONG_X',
  FLOAT: 'DOUBLE_X',
  AMOUNT: 'AMOUNT',
  STRING: 'CHARACTER_X',
  PARAMETER: 'PARAMETER',
  DICTIONARY: 'DIC',
  ARRAY: 'ARRAY',
  BOOLEAN: 'BOOLEAN_X',
};

export const RULE_FAC_TYPE = {
  INTEGER: 'INTEGER',
  LONGINTEGER: 'LONGINTEGER',
  FLOAT: 'FLOAT',
  AMOUNT: 'AMOUNT',
  STRING: 'STRING',
  PARAMETER: 'PARAMETER',
  DICTIONARY: 'DICTIONARY',
  ARRAY: 'ARRAY',
  BOOLEAN: 'BOOLEAN',
};

export const RULE_FAC_OP_TYPE = {
  EQ: 'EQ',
  GT: 'GT',
  LT: 'LT',
  NEQ: 'NEQ',
  GE: 'GE',
  LE: 'LE',
  IN: 'IN',
  HASVAL: 'HASVAL',
  NOHASVAL: 'NOHASVAL',
  NULL: 'NULL',
  NONULL: 'NONULL',
  BETWEEN: 'BETWEEN',
  LIKE: 'LIKE',
  NOLIKE: 'NOLIKE',
  STARTSWITH: 'STARTSWITH',
  NOTSTARTSWITH: 'NOTSTARTSWITH',
  ENDSWITH: 'ENDSWITH',
  INLIST: 'INLIST',
  NOTINLIST: 'NOTINLIST',
  NOTIN: 'NOTIN',
  CUTEQ: 'CUTEQ',
  CUTNOTEQ: 'CUTNOTEQ',
  CUTIN: 'CUTIN',
  DIVIDE: 'DIVIDE',
  NOTDIVIDE: 'NOTDIVIDE',
  NOEMPTY: 'NOEMPTY',
  EQIGNORECASE: 'EQIGNORECASE',
};

/**
 * 规则布尔型枚举
 */
export const RULE_BOOLEAN_TYPE = {
  true: 'true',
  false: 'false',
};

/**
 * 规则因子字符型枚举
 */
export const RULE_CHAR_LIST = {
  UPPER_CASE_EN: '1',
  LOWER_CASE_EN: '2',
  SPECIAL_CHAR: '3',
  CHINESE: '4',
  NUMBER: '5',
};

export const RULE_CHAR_TYPE = {
  UPPER_CASE_EN: 'UPPER_CASE_EN',
  LOWER_CASE_EN: 'LOWER_CASE_EN',
  SPECIAL_CHAR: 'SPECIAL_CHAR',
  CHINESE: 'CHINESE',
  NUMBER: 'NUMBER',
};

/**
 * 处理错误格式
 */
export const RULE_ERROR_TYPE = {
  valueNull: 'valueNull', // 值为空
  checkCharFormat: 'checkCharFormat', // 字符串填写不规范
  opExpsValueNull: 'opExpsValueNull', // 运算表达式值或规则因子为空
  opExpsFuncNotMeet: 'opExpsFuncNotMeet', // 运算表达式函数必须有两个子节点以上
  judgeCondGroupNotMeet: 'judgeCondGroupNotMeet', // 条件表达式条件组合必须有一个子节点
  judgeCondEditing: 'judgeCondEditing', // 条件表达式条件在编辑状态
};

/**
 * 条件表达式中数值类型的规则因子可以选择的运算表达式枚举
 */
export const RULE_NUMBER_OPEXPS_LIST = [
  { key: 'opExps', value: 'opExps', label: 'opExps', valueWorldSegType: 'OOEXPS', wordSegBid: 'opExps' },
];

/**
 * 条件中获取的值选单类型
 */
export const RULE_COND_MENU_TYPE = {
  opExps: 'opExps', // 运算表达式
  paramter: 'paramter', // 参数
  dictionary: 'dictionary', // 字典
};
