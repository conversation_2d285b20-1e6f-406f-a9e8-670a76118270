/* 公共样式 */

/* 布局 */
.flex-1 {
  display: flex;
  flex: 1;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-align-center {
  align-items: center;
}
.flex-justify-center {
  justify-content: center;
}
.flex-justify-around {
  justify-content: space-around;
}
.flex-justify-between {
  justify-content: space-between;
}
/* 盒子 */
.common-margin {
  margin: var(--margin-padding);
}
.common-padding {
  padding: var(--margin-padding);
}
.m-l {
  margin-left: var(--margin-padding);
}
.m-r {
  margin-right: var(--margin-padding);
}
.m-t {
  margin-top: var(--margin-padding);
}
.m-b {
  margin-bottom: var(--margin-padding);
}
.m-tb {
  margin-top: var(--margin-padding);
  margin-bottom: var(--margin-padding);
}
.m-lr {
  margin-left: var(--margin-padding);
  margin-right: var(--margin-padding);
}
.m-l-s {
  margin-left: var(--margin-padding-s);
}
.m-r-s {
  margin-right: var(--margin-padding-s);
}
.m-t-s {
  margin-top: var(--margin-padding-s);
}
.m-b-s {
  margin-bottom: var(--margin-padding-s);
}
.m-tb-s {
  margin-top: var(--margin-padding-s);
  margin-bottom: var(--margin-padding-s);
}
.m-lr-s {
  margin-left: var(--margin-padding-s);
  margin-right: var(--margin-padding-s);
}
.p-l {
  padding-left: var(--margin-padding);
}
.p-r {
  padding-right: var(--margin-padding);
}
.p-t {
  padding-top: var(--margin-padding);
}
.p-b {
  padding-bottom: var(--margin-padding);
}
.p-tb {
  padding-top: var(--margin-padding);
  padding-bottom: var(--margin-padding);
}
.p-lr {
  padding-left: var(--margin-padding);
  padding-right: var(--margin-padding);
}
.p-l-s {
  padding-left: var(--margin-padding-s);
}
.p-r-s {
  padding-right: var(--margin-padding-s);
}
.p-t-s {
  padding-top: var(--margin-padding-s);
}
.p-b-s {
  padding-bottom: var(--margin-padding-s);
}
.p-tb-s {
  padding-top: var(--margin-padding-s);
  padding-bottom: var(--margin-padding-s);
}
.p-lr-s {
  padding-left: var(--margin-padding-s);
  padding-right: var(--margin-padding-s);
}
/* 尺寸 */
.height100 {
  height: 100%;
}
.height80 {
  height: 80%;
}
.height60 {
  height: 60%;
}
.height50 {
  height: 50%;
}
.height40 {
  height: 40%;
}
.height20 {
  height: 20%;
}
.width100 {
  width: 100%;
}
.width80 {
  width: 80%;
}
.width60 {
  width: 60%;
}
.width50 {
  width: 50%;
}
.width40 {
  width: 40%;
}
.width20 {
  width: 30%;
}
/* 表现 */
.font-size-n {
  font-size: var(--font-size);
}
.font-size-s {
  font-size: var(--font-size-s);
  font-weight: 300;
}
.font-size-l {
  font-size: var(--font-size-l);
  font-weight: 500;
}
.font-size-xl {
  font-size: var(--font-size-xl);
  font-weight: 600;
}
.primary-color {
  color: var(--color-main);
}
.error-color {
  color: var(--error-color);
}
.warning-color {
  color: var(--warning-color);
}
.success-color {
  color: var(--success-color);
}
.btn-color {
  background-image: linear-gradient(135deg, var(--color-main), var(--color-three));
}
.btn-color:active {
  background-color: var(--color-five) !important;
}
.btn-color:hover {
  background-color: var(--color-three) !important;
}
.btn-color:where(.css-dev-only-do-not-override-apn68) {
  transition: none !important;
}
/* 页面级别样式 */
.app-block,
.app-container {
  border-radius: 0.5rem;
  padding: 0 1.5rem;
  box-shadow: 0 0.15rem 0.3rem rgba(0, 0, 0, 0.06), 0 0 0.45rem rgba(0, 0, 0, 0.03);
  background: var(--gray-one);
}

.app-overflow {
  height: 100%;
  overflow: auto;
}
.app-loading {
  width: 100%;
  height: 100%;
  display: flex;
}
.app-table {
  display: flex;
  flex-direction: column;
}
/* 表单操作固定行 */
.form-action {
  display: flex;
  justify-content: space-between;
  height: 3rem;

  > span {
    font-size: var(--font-size-l);
    color: var(--color-main);
  }
}
.form-action-bottom {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 3rem;
  margin-top: 0.5rem;
  border-top: 2px solid var(--gray-two);
}
.form-title-prefix {
  margin-right: 1rem;
  height: 1.6rem;
  width: 0.4rem;
  background: var(--color-main);
}
/* 搜索框样式 */
.search-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
}
/* 主题字体和背景色 */
.theme-color-main {
  color: var(--gray-light);
  background-color: var(--color-main);
  /* background: linear-gradient(to bottom, var(--color-main) 0%, var(--color-six) 100%); */
}