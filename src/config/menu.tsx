import {
  ChromeFilled,
  CrownFilled,
  SmileFilled,
  TabletFilled,
} from '@ant-design/icons';

export default {
  path: '/',
  routes: [
    {
      path: '/home',
      name: '首页',
      icon: <CrownFilled />,
      children: [] // 添加空的children数组
    },
    {
      path: '/access', 
      name: '权限演示',
      icon: <CrownFilled />,
      children: [] // 添加空的children数组
    },
    {
      path: '/table',
      name: 'CRUD 示例',
      icon: <TabletFilled />,
      children: [] // 添加空的children数组
    },
  ],
};