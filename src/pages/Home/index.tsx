import React, { useState } from 'react';
import { Button, Input, message, Space } from 'antd';
import { ethers } from 'ethers';

export default function WalletDemo() {
  const [userAddress, setUserAddress] = useState('');
  const [amount, setAmount] = useState('');
  const [platformWallet, setPlatformWallet] = useState<{address: string, privateKey: string} | null>(null);

  // 🔹 初始化平台钱包（只演示用，真实环境请后端生成并存私钥）
  const createPlatformWallet = () => {
    const wallet = ethers.Wallet.createRandom();
    setPlatformWallet({address: wallet.address, privateKey: wallet.privateKey});
    message.success(`已创建平台钱包: ${wallet.address}`);
  };

  // 🔹 切换MetaMask到Sepolia
  const switchToSepolia = async () => {
    const chainId = '0xaa36a7'; // 11155111 十六进制
    try {
      await (window as any).ethereum.request({
        method: 'wallet_switchEthereumChain',
        params: [{ chainId }],
      });
    } catch (err: any) {
      if (err.code === 4902) {
        await (window as any).ethereum.request({
          method: 'wallet_addEthereumChain',
          params: [{
            chainId,
            chainName: 'Sepolia Test Network',
            rpcUrls: ['https://sepolia.infura.io/v3/你的InfuraKey'],
            nativeCurrency: { name: 'SepoliaETH', symbol: 'ETH', decimals: 18 },
            blockExplorerUrls: ['https://sepolia.etherscan.io'],
          }]
        });
      }
    }
    message.success('已切换到Sepolia');
  };

  // 🔹 连接MetaMask
  const connectMetaMask = async () => {
    const provider = new ethers.BrowserProvider((window as any).ethereum);
    await provider.send('eth_requestAccounts', []);
    const signer = await provider.getSigner();
    const addr = await signer.getAddress();
    setUserAddress(addr);
    message.success(`已连接MetaMask: ${addr}`);
  };

  // 🔹 MetaMask充值到平台钱包
  const sendToPlatformWallet = async () => {
    if (!platformWallet) return message.error('请先创建平台钱包');
    if (!userAddress) return message.error('请先连接MetaMask');
    if (!amount) return message.error('请输入金额');

    const provider = new ethers.BrowserProvider((window as any).ethereum);
    const signer = await provider.getSigner();
    const tx = await signer.sendTransaction({
      to: platformWallet.address,
      value: ethers.parseEther(amount)
    });
    message.success(`充值交易已提交: ${tx.hash}`);
  };

  // 🔹 平台钱包提现到用户MetaMask（演示用，真实环境后端执行）
  const withdrawToUser = async () => {
    if (!platformWallet) return message.error('请先创建平台钱包');
    if (!userAddress) return message.error('请先连接MetaMask');
    if (!amount) return message.error('请输入金额');

    const provider = new ethers.JsonRpcProvider('https://sepolia.infura.io/v3/你的InfuraKey');
    const wallet = new ethers.Wallet(platformWallet.privateKey, provider);
    const tx = await wallet.sendTransaction({
      to: userAddress,
      value: ethers.parseEther(amount)
    });
    message.success(`提现交易已提交: ${tx.hash}`);
  };

  return (
    <div>
      <Space>
        <Button onClick={createPlatformWallet}>创建平台钱包</Button>
        <Button onClick={switchToSepolia}>切换Sepolia</Button>
        <Button onClick={connectMetaMask}>连接MetaMask</Button>
      </Space>
      <div style={{marginTop:10}}>
        用户地址: {userAddress}
      </div>
      <div>
        平台钱包: {platformWallet?.address || '未创建'}
      </div>
      <Input placeholder="金额(ETH)" value={amount} onChange={e => setAmount(e.target.value)} style={{width:200, marginTop:10}}/>
      <Space style={{marginTop:10}}>
        <Button type="primary" onClick={sendToPlatformWallet}>MetaMask→平台钱包充值</Button>
        <Button onClick={withdrawToUser}>平台钱包→MetaMask提现</Button>
      </Space>
    </div>
  );
}
